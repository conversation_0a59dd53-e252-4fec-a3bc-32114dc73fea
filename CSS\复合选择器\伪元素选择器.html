<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>伪元素选择器</title>
    <!-- 伪元素：很像元素，但不是元素（element），是元素中的一些特殊位置 -->
    <style>
        /* 选中的是div中的第一个字母 */
        div::first-letter{
            color:red;
            font-size: 40px;
        }

        /* 选中的是div中的第一行 */
        div::first-line{
            background-color: yellow;
        }

        /* 选中的是被鼠标选择的文字 */
        div::selection{
            background-color: green;
            color: orange;
        }

        /* 选中的是input元素中的提示文字 */
        input::placeholder{
            color: skyblue;
        }

        /* 选中的是P元素最开始的位置，随后创建一个子元素 */
        p::before{
            content: "￥";
            color: red;
        }

        /* 选中的是P元素最后的位置，随后创建一个子元素 */
        p::after{
            content:".00"
        }
    </style>
</head>
<body>
    <div>Lorem ipsum dolor sit amet consectetur, adipisicing elit. I<PERSON>am corporis, quibusdam eveniet rerum id exercitationem aperiam nobis eius cumque mollitia quos quo laborum fugiat? Perferendis excepturi odio hic similique vero!</div>
    <br>
    <input type="text" placeholder="请输入您的用户名">
    <P>199</P>
    <P>299</P>
    <P>399</P>
    <P>499</P>
</body>
</html>