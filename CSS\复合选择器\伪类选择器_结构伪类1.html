<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结构为类1</title>
    <style>
        /* 选中的是div的第一个儿子P元素（按照所有子元素计算） —— 看结构一 */
        /* div>p:first-child{
            color: red;
        } */

        /* 选中的是div的第一个儿子P元素（按照所有子元素计算） —— 看结构二 */
        /* div>p:first-child{
            color: red;
        } */

        /* 选中的是div的后代P元素，且p的父级是谁无所谓，但p必须是其父级的第一个儿子（按照所有子元素计算） —— 看结构三 */
        /* div p:first-child {
            color: red;
        } */

        /* 选中的是P元素，且p的父级是谁无所谓，但p必须是其父级的第一个儿子（按照所有子元素计算） —— 看结构三 */
        p:first-child {
            color: red;
        }
    </style>
</head>

<body>
    <!-- 结构一 -->
    <!-- <div>
        <p>张三：98分</p>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
    </div> -->

    <!-- 结构二 -->
    <!-- <div>
        <span>张三：98分</span>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
    </div> -->

    <!-- 结构三 -->
    <!-- <p>测试1</p>
    <div>
        <p>测试2</p>
        <marquee>
            <p>测试3</p>
            <p>张三：98分</p>
        </marquee>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
    </div> -->

    <!-- 结构三 -->
    <p>测试1</p>
    <div>
        <p>测试2</p>
        <marquee>
            <p>测试3</p>
            <p>张三：98分</p>
        </marquee>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
    </div>
</body>

</html>