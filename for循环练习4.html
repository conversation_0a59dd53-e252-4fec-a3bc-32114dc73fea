<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>For循环练习 嵌套的for循环</title>
  <style></style>
</head>

<body>
  <script>
    /*for (i = 0; i < 5; i++) {
      for (j = 0; j < i+1; j++) {
        document.write("*&nbsp;");
      }
      document.write("<br>");
    }*/


    //打印1-100之间所有的质数
    //质数：只能被1和它本身整除的数，1不是质数也不是合数，质数必须是大于1的自然数
    document.write("1-100之间所有的质数为：<br>");
    var flag = true;
    for (var i = 2; i <= 100; i++) {
      //判断i是否为质数
      for (var j = 2; j < i; j++) {
        if (i % j == 0) {
          flag = false;
          break;
        }
        if (flag = true) {
          document.write(i + "<br>");
        }
      }

    }

    //打印99乘法表
    /*for(i=1;i<=9;i++){
      for(j=1;j<=i;j++){
        document.write(i+"*"+j+"="+i*j+"&nbsp;&nbsp;");
      }
      document.write("<br>");
    }*/

  </script>
</body>