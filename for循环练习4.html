<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>For循环练习 嵌套的for循环</title>
  <style>
    span{
      display: inline-block;
      width: 50px;
    }
  </style>
</head>

<body>
  <script>
    //打印星号三角形
    /*for (i = 0; i < 5; i++) {
      //外层循环控制行数，共5行
      for (j = 0; j < i+1; j++) {
        //内层循环控制每行的星号个数，第i行打印i+1个星号
        document.write("*&nbsp;"); //打印星号和空格
      }
      document.write("<br>"); //每行结束后换行
    }*/


    //打印1-100之间所有的质数
    //质数：只能被1和它本身整除的数，1不是质数也不是合数，质数必须是大于1的自然数
   /* document.write("1-100之间所有的质数为：<br>");
    for (var i = 2; i <= 100; i++) {
      var flag = true; //假设i是质数
      //判断i是否为质数
      for (var j = 2; j < i; j++) {
        if (i % j == 0) {
          flag = false; //如果能被j整除，说明不是质数
          break; //跳出内层循环
        }
      }
      //如果flag仍为true，说明i是质数
      if (flag == true) {
        document.write(i + "<br>");
      }
    }*/

    //打印99乘法表
    for(i=1;i<=9;i++){
      for(j=1;j<=i;j++){
        document.write("<span>"+i+"*"+j+"="+i*j+"</span>");
      }
      document.write("<br>");
    }
    

  </script>
</body>