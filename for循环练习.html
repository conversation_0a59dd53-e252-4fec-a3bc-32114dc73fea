<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>For循环练习</title>
  <style></style>
</head>

<body>
  <script>
    var sum = 0;
    var count=0;
    for(i=1;i<=100;i++){
      if(i%7==0){
        sum=sum+i;
        count++;
      }
    }
    document.write("1到100之间能被7整除的数之和为："+sum+"<br>");
    document.write("1到100之间能被7整除的数的个数为："+count);
  </script>
</body>