<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>否定伪类</title>
    <style>
        /* 选中的是div的儿子P元素，但是不选中类名为fail的P元素 */
        /* div>p:not(.fail){
            color: red;
        } */

        /* 选中的是div的儿子P元素，但是不选中title属性值以“你要加油”开头的P元素 */
        /* div>p:not([title^="你要加油"]){
            color:red;
        } */

        /* 选中的是div的儿子P元素，但是不选中第一个P元素 */
        div>p:not(:first-child){
            color: red;
        }
    </style>
</head>

<body>
    <div>
        <p>张三：98分</p>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
        <p class="fail" title="你要加油啊！孙七！">孙七：58分</p>
        <p class="fail" title="你要加油啊！周八！">周八：48分</p>

    </div>
</body>

</html>