<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Switch练习 - 成绩判断</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .result {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .success {
      border-left: 4px solid #4CAF50;
      color: #2E7D32;
    }
    .fail {
      border-left: 4px solid #f44336;
      color: #C62828;
    }
    .error {
      border-left: 4px solid #ff9800;
      color: #E65100;
    }
    h1 {
      text-align: center;
      color: #333;
    }
  </style>
</head>

<body>
  <h1>🎯 Switch练习 - 成绩判断</h1>

  <script>
    // 获取用户输入并转换为数字
    // var score = +prompt("请输入你的成绩（0-100）:");

    // 验证输入是否有效
    if (isNaN(score) || score < 0 || score > 100) {
      document.write('<div class="result error">');
      document.write('<h2>❌ 输入错误</h2>');
      document.write('<p>请输入0-100之间的有效数字！</p>');
      document.write('</div>');
    } else {
      // 显示输入的成绩
      document.write('<div class="result">');
      document.write('<h2>📊 你的成绩</h2>');
      document.write('<p>成绩：<strong>' + score + '</strong> 分</p>');
      document.write('</div>');

      // 使用switch语句判断
      switch (true) {
        case score >= 60:
          console.log("合格！");
          document.write('<div class="result success">');
          document.write('<h2>🎉 合格！</h2>');
          document.write('<p>恭喜你，成绩达到了及格线！</p>');
          document.write('</div>');
          break;
        default:
          console.log("不合格！");
          document.write('<div class="result fail">');
          document.write('<h2>😞 不合格</h2>');
          document.write('<p>很遗憾，成绩未达到及格线。继续努力！</p>');
          document.write('</div>');
          break;
      }
    }
  </script>

  <!-- 代码说明 -->
  <div class="result">
    <h2>📝 代码说明</h2>
    <p><strong>switch (true)</strong> 是一个特殊用法：</p>
    <ul>
      <li>当switch的值是true时，会执行第一个结果为true的case</li>
      <li><code>case score >= 60:</code> 如果成绩>=60，这个表达式为true，就执行这个case</li>
      <li>如果没有case为true，就执行default</li>
    </ul>

    <h3>💡 你的代码很聪明！</h3>
    <p>使用 <code>switch (true)</code> 是一个高级技巧，可以在switch中使用条件判断。</p>
    <p>主要问题只是缺少页面显示效果。</p>
  </div>
</body>

</html>