<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态伪类</title>

    <style>
        /* 选中的是没有访问过的a标签 */
        a:link {
            color: orange;
        }

        /* 选中的是访问过的a标签 */
        a:visited {
            color: green;
        }

        /* 选中的是鼠标悬停的a标签 */
        a:hover {
            color: skyblue;
        }

        /* 选中的是激活状态的a标签 */
        a:active {
            color: gray;
        }

        /* 选中的是鼠标悬浮状态的soan元素 */
        span:hover {
            color: purple;
        }

        /* 选中的是激活状态的span元素 */
        span:active {
            color: pink;
        }

        input:focus ,select:focus{
            color: orange;
            background-color: green;
        }

    </style>
</head>

<body>
    <a href="https://www.baidu.com">去百度</a>
    <a href="https://www.jd.com">去京东</a>
    <br>
    <span>毛茸茸</span>
    <br>
    <input type="text">
    <br>
    <input type="text">
    <br>
    <input type="text">
    <select>
        <option value="bj">北京</option>
        <option value="sh">上海</option>
        <option value="gz">广州</option>
    </select>

</body>

</html>