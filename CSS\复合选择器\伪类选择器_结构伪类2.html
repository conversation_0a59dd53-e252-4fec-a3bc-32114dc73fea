<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结构为类2</title>
    <style>
        /* 选中的是div的第一个儿子P元素（按照所有子元素计算） —— 看结构一 */
        /* div>p:first-child{
            color: red;
        } */

        /* 选中的是div的最后一个儿子P元素（按照所有子元素计算） —— 看结构一 */
        /* div>p:last-child{
            color: red;
        } */

        /* 选中的是div的第n个儿子P元素（按照所有子元素计算） —— 看结构一 */
        /* div>p:nth-child(3){
            color: red;
        } */

        /* 选中的是div的第n个儿子P元素（按照所有子元素计算） —— 看结构二 
        0或者什么都不写 什么都不选中
        1~正无穷的整数 选中对应序号的子元素
        2n或even 选中序号为偶数的子元素
        2n+1或odd 选中序号为奇数的子元素
        -n+3 选中前三个子元素
        */
        /* div>p:nth-child(1){
            color: red;
        } */

        /* 选中的是div的第一个儿子P元素（按照所有同类型兄弟计算） —— 看结构三 */
         /* div>p:first-of-type{
            color: red;
        } */

        /* 选中的是div的最后一个儿子P元素（按照所有同类型兄弟计算） —— 看结构三 */
        /* div>p:last-of-type{
            color: red;
        } */

        /* 选中的是div的第n个儿子P元素（按照所有同类型兄弟计算） —— 看结构三 */
        div>p:nth-of-type(3){
            color: red;
        }
    </style>
</head>

<body>
    <!-- 结构一 -->
    <div>
        <p>张三：98分</p>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
        <p>孙七：58分</p>
        <p>周八：48分</p>
    </div>

        <!-- 结构二 -->
    <div>
        <p>第1个</p>
        <p>第2个</p>
        <p>第3个</p>
        <p>第4个</p>
        <p>第5个</p>
        <p>第6个</p>
        <p>第7个</p>
        <p>第8个</p>
        <p>第9个</p>
        <p>第10个</p>
    </div>

        <!-- 结构三 -->
    <div>
        <span>测试</span>
        <p>张三：98分</p>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
        <p>孙七：58分</p>
        <p>周八：48分</p>
    </div>
</body>

</html>