<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>vertical-align</title>
    <style>
        .container{
            font-size: 100px;
            height: 300px;
            background-color: skyblue;
            margin: 20px 0;
            border: 2px solid red; /* 显示div边界 */
        }

        .demo-span{
            font-size: 40px;
            background-color: orange;
            border: 2px solid blue; /* 显示span边界 */
        }

        .top { vertical-align: top; }
        .middle { vertical-align: middle; }
        .bottom { vertical-align: bottom; }
        .baseline { vertical-align: baseline; }

        /* 添加说明文字样式 */
        .explanation {
            font-size: 16px;
            color: #333;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="explanation">vertical-align: top - span顶端对齐到行盒子顶端</div>
    <div class="container">
        大番薯x<span class="demo-span top">x上锅蒸熟</span>
    </div>

    <div class="explanation">vertical-align: middle - span中线对齐到行盒子中线</div>
    <div class="container">
        大番薯x<span class="demo-span middle">x上锅蒸熟</span>
    </div>

    <div class="explanation">vertical-align: bottom - span底端对齐到行盒子底端</div>
    <div class="container">
        大番薯x<span class="demo-span bottom">x上锅蒸熟</span>
    </div>

    <div class="explanation">vertical-align: baseline (默认) - span基线对齐到行盒子基线</div>
    <div class="container">
        大番薯x<span class="demo-span baseline">x上锅蒸熟</span>
    </div>
</body>
</html>