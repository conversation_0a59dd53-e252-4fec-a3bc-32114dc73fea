<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Switch基础练习</title>
</head>
<body>
    <h1>Switch语句基础练习</h1>
    
    <script>
        // 获取用户输入的成绩
        var score = prompt("请输入你的成绩:");
        
        // 将字符串转换为数字
        score = Number(score);
        
        // 使用switch语句判断合格/不合格
        // 关键：先判断成绩是否大于等于60，然后用switch
        var result = score >= 60 ? "合格" : "不合格";
        
        switch (result) {
            case "合格":
                alert("恭喜！你的成绩合格了！");
                document.write("<h2>🎉 恭喜！你的成绩合格了！</h2>");
                document.write("<p>你的成绩：" + score + " 分</p>");
                break;
                
            case "不合格":
                alert("很遗憾，你的成绩不合格。");
                document.write("<h2>😞 很遗憾，你的成绩不合格</h2>");
                document.write("<p>你的成绩：" + score + " 分</p>");
                document.write("<p>继续努力，下次一定能考好！</p>");
                break;
        }
    </script>
    
    <!-- 代码说明 -->
    <hr>
    <h2>📝 代码解释</h2>
    <p><strong>第1步：</strong>获取用户输入 <code>var score = prompt("请输入你的成绩:");</code></p>
    <p><strong>第2步：</strong>转换为数字 <code>score = Number(score);</code></p>
    <p><strong>第3步：</strong>判断合格状态 <code>var result = score >= 60 ? "合格" : "不合格";</code></p>
    <p><strong>第4步：</strong>使用switch语句根据结果输出不同内容</p>
    
    <h3>💡 为什么这样写？</h3>
    <p>因为switch语句只能比较<strong>相等的值</strong>，不能直接写条件判断（如 >= 60）。</p>
    <p>所以我们先用三元运算符判断出"合格"或"不合格"，然后用switch来处理。</p>
    
</body>
</html>
