<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本修饰</title>
    <style>
        div {
            font-size: 40px;
        }
        /* 上划的绿色虚线 */
        .shu1{
            text-decoration: overline dotted green;
        }
        /* 删除的红色波浪线 */
        .shu2{
            text-decoration: line-through wavy red;
        }
        /* 下划线 */
        .shu3{
            text-decoration: underline;
        }
        .shu4,ins,del{
            font-size: 40px;
            /* 没有各种线 */
            text-decoration: none;
        }
    </style>
</head>

<body>
    <div class="shu1">大番薯1</div>
    <div class="shu2">大番薯2</div>
    <div class="shu3">大番薯3</div>
    <a class="shu4" href="http://www.baidu.com">大番薯4</a>
    <ins>测试1</ins>
    <del>测试2</del>
</body>

</html>