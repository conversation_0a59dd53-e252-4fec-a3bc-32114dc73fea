<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>属性选择器</title>
    <style>
        /* 第一种写法：选中具有title属性的元素 */
        /* [title]{
            color: red;
        } */

        /* 第二种写法：选中title属性值为atguigu2的元素 */
        [title="atguigu2"]{
            color: green;
        }

        /* 第三种写法：选中title属性值以a开头的元素 */
        /* [title^="a"]{
            color: blue;
        } */

        /* 第四种写法：选中title属性值以u结尾的元素 */
        /* [title$="u"]{
            color: blue;
        } */

        /* 第五种写法：选中title属性值包含a的元素 */
        /* [title*="a"]{
            color: blue;
        } */
    </style>
</head>
<body>
    <div title="atguigu1">尚硅谷1</div>
    <div title="atguigu2">尚硅谷2</div>
    <div title="guigu3">尚硅谷3/div>
    <div title="guigu4">尚硅谷4</div>
</body>
</html>