<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兄弟选择器</title>
    <style>
        /* 选中div后面紧紧相邻的兄弟p元素（睡在我下铺的兄弟）——相邻兄弟选择器 */
        div + p {
            color: red;
        }

        /* 选中div后面所有的兄弟p元素（睡在我下铺的兄弟和后面的所有兄弟）——通用兄弟选择器 */
        /* div ~ p {
            color: green;
        } */
        /* li~li{
            color: pink;
        } */
        li+li{
            color: purple;
        }
    </style>
</head>
<body>
    <div>尚硅谷</div>
    <p>前端</p>
    <P>java</P>
    <p>大数据</p>
    <p>UI</p>
    <ul>
        <li>主页</li>
        <li>秒杀</li>
        <li>订单</li>
        <li>我的</li>

    </ul>
</body>
</html>