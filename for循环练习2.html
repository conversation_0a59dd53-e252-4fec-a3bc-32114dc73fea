<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>For循环练习 水仙花数</title>
  <style></style>
</head>

<body>
    <script>
        /*水仙花数是指一个 3 位数，它的每个位上的数字的 3次幂之和等于它本身。
        例如：153 = 1^3 + 5^3 + 3^3。*/

        //先打印所有三位数
        for(var i = 100 ; i <= 999;i++){
            //获取i的百位 十位 个位上的数字
            //获取百位上的数字
            var bai = parseInt(i/100);
            
            //获取十位上的数字
            var shi = parseInt((i-bai*100)/10);

            //获取个位上的数字
            var ge = i%10;

            //判断是否是水仙花数
            if(bai*bai*bai+shi*shi*shi+ge*ge*ge==i){
                document.write(i+"<br>");
            }
        }
    </script>
</body>