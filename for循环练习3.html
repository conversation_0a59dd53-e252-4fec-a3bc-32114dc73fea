<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>For循环练习 判断质数</title>
  <style></style>
</head>

<body>
  <script>
    /*在页面中接收一个用户输入的数字，判断这个数字是否是质数。
    质数：只能被1和它本身整除的数，1不是质数也不是合数，质数必须是大于1的自然数*/
    var num = +prompt("请输入一个大于1的整数：");

    //创建一个变量来保存当前数的状态，默认当前num为质数
    var flag = true;

    //判断这个数是否合法
    if (num <= 1) {
      alert("请输入一个大于1的整数！");
    } else {
      //判断num是否为质数
      //获取2—num之间的数
      for (var i = 2; i < num; i++) {
        if (num % i == 0) {
          //此处判定num不是质数
          //设置flag为false
          flag = false;
          break;
        }
      }
      if (flag) {
        alert(num + "是质数！！！");
      }
      else{
        alert(num + "不是质数！！！");
      }

    }
  </script>
</body>