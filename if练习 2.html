<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>比较三个数字大小</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f0f0f0;
      padding: 20px;
    }

    .result {
      background-color: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin: 20px 0;
      font-size: 18px;
      line-height: 1.6;
    }

    h1 {
      color: #333;
      text-align: center;
    }

    .highlight {
      color: #007acc;
      font-weight: bold;
    }

    .code-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin: 10px 0;
      border-left: 4px solid #007acc;
    }
  </style>
</head>

<body>
  <h1>🔢 比较三个数字大小</h1>

  <script>
    // 获取用户输入
    var num1 = +prompt("请输入第一个数字:");
    var num2 = +prompt("请输入第二个数字:");
    var num3 = +prompt("请输入第三个数字:");

    // 验证输入
    if (isNaN(num1) || isNaN(num2) || isNaN(num3)) {
      document.write('<div class="result">❌ 请输入有效的数字！</div>');
    } else {
      // 显示输入的数字
      document.write('<div class="result">');
      document.write('<h2>📊 输入的数字</h2>');
      document.write('第一个数字：<span class="highlight">' + num1 + '</span><br>');
      document.write('第二个数字：<span class="highlight">' + num2 + '</span><br>');
      document.write('第三个数字：<span class="highlight">' + num3 + '</span><br>');
      document.write('</div>');

      // 显示排序结果
      document.write('<div class="result">');
      document.write('<h2>🏆 排序结果（从小到大）</h2>');

      // 修复后的if语句逻辑 - 使用 <= 来处理相等情况
      if (num1 <= num2 && num1 <= num3) {
        // num1最小，比较num2和num3
        if (num2 <= num3) {
          document.write('<span class="highlight">' + num1 + ' ≤ ' + num2 + ' ≤ ' + num3 + '</span>');
          console.log('情况1: ' + num1 + ',' + num2 + ',' + num3);
        } else {
          document.write('<span class="highlight">' + num1 + ' ≤ ' + num3 + ' ≤ ' + num2 + '</span>');
          console.log('情况2: ' + num1 + ',' + num3 + ',' + num2);
        }
      } else if (num2 <= num1 && num2 <= num3) {
        // num2最小，比较num1和num3
        if (num1 <= num3) {
          document.write('<span class="highlight">' + num2 + ' ≤ ' + num1 + ' ≤ ' + num3 + '</span>');
          console.log('情况3: ' + num2 + ',' + num1 + ',' + num3);
        } else {
          document.write('<span class="highlight">' + num2 + ' ≤ ' + num3 + ' ≤ ' + num1 + '</span>');
          console.log('情况4: ' + num2 + ',' + num3 + ',' + num1);
        }
      } else {
        // num3最小，比较num1和num2
        if (num1 <= num2) {
          document.write('<span class="highlight">' + num3 + ' ≤ ' + num1 + ' ≤ ' + num2 + '</span>');
          console.log('情况5: ' + num3 + ',' + num1 + ',' + num2);
        } else {
          document.write('<span class="highlight">' + num3 + ' ≤ ' + num2 + ' ≤ ' + num1 + '</span>');
          console.log('情况6: ' + num3 + ',' + num2 + ',' + num1);
        }
      }
      document.write('</div>');

      // 显示逻辑分析
      document.write('<div class="result">');
      document.write('<h2>🧠 if语句逻辑分析</h2>');
      document.write('<div class="code-section">');
      document.write('<h3>步骤1：找最小值</h3>');

      if (num1 <= num2 && num1 <= num3) {
        document.write('✅ <strong>if (num1 <= num2 && num1 <= num3)</strong><br>');
        document.write('→ ' + num1 + ' 是最小值（或并列最小）<br>');
      } else if (num2 <= num1 && num2 <= num3) {
        document.write('✅ <strong>else if (num2 <= num1 && num2 <= num3)</strong><br>');
        document.write('→ ' + num2 + ' 是最小值（或并列最小）<br>');
      } else {
        document.write('✅ <strong>else</strong><br>');
        document.write('→ ' + num3 + ' 是最小值（或并列最小）<br>');
      }

      document.write('<h3>步骤2：比较剩余两个数字</h3>');
      document.write('在确定最小值后，再比较剩余两个数字的大小关系');
      document.write('</div>');
      document.write('</div>');
    }


    /*
     // 获取用户输入的三个数字
     var num1 = prompt("请输入第一个数字:");
     var num2 = prompt("请输入第二个数字:");
     var num3 = prompt("请输入第三个数字:");
 
     // 将字符串转换为数字
     num1 = parseFloat(num1);
     num2 = parseFloat(num2);
     num3 = parseFloat(num3);
 
     // 验证输入是否为有效数字
     if (isNaN(num1) || isNaN(num2) || isNaN(num3)) {
         document.write('<div class="result">❌ 请输入有效的数字！</div>');
     } else {
         // 显示输入的数字
         document.write('<div class="result">');
         document.write('<h2>📊 输入的数字</h2>');
         document.write('第一个数字：<span class="highlight">' + num1 + '</span><br>');
         document.write('第二个数字：<span class="highlight">' + num2 + '</span><br>');
         document.write('第三个数字：<span class="highlight">' + num3 + '</span><br>');
         document.write('</div>');
 
         // 使用if语句比较三个数字的大小
         var max, min, middle;
         var resultText = '';
 
         // 方法1：找出最大值
         if (num1 >= num2 && num1 >= num3) {
             max = num1;
         } else if (num2 >= num1 && num2 >= num3) {
             max = num2;
         } else {
             max = num3;
         }
 
         // 方法2：找出最小值
         if (num1 <= num2 && num1 <= num3) {
             min = num1;
         } else if (num2 <= num1 && num2 <= num3) {
             min = num2;
         } else {
             min = num3;
         }
 
         // 找出中间值
         if (num1 !== max && num1 !== min) {
             middle = num1;
         } else if (num2 !== max && num2 !== min) {
             middle = num2;
         } else {
             middle = num3;
         }
 
         // 显示比较结果
         document.write('<div class="result">');
         document.write('<h2>🏆 比较结果</h2>');
 
         // 处理特殊情况：三个数字相等
         if (num1 === num2 && num2 === num3) {
             document.write('🎯 三个数字相等：<span class="highlight">' + num1 + ' = ' + num2 + ' = ' + num3 + '</span>');
         }
         // 处理两个数字相等的情况
         else if (num1 === num2 || num2 === num3 || num1 === num3) {
             document.write('最大值：<span class="highlight">' + max + '</span><br>');
             document.write('最小值：<span class="highlight">' + min + '</span><br>');
 
             if (num1 === num2) {
                 document.write('🔗 ' + num1 + ' 和 ' + num2 + ' 相等');
             } else if (num2 === num3) {
                 document.write('🔗 ' + num2 + ' 和 ' + num3 + ' 相等');
             } else {
                 document.write('🔗 ' + num1 + ' 和 ' + num3 + ' 相等');
             }
         }
         // 三个数字都不相等
         else {
             document.write('最大值：<span class="highlight">' + max + '</span><br>');
             document.write('中间值：<span class="highlight">' + middle + '</span><br>');
             document.write('最小值：<span class="highlight">' + min + '</span><br>');
             document.write('📈 从大到小排序：<span class="highlight">' + max + ' > ' + middle + ' > ' + min + '</span>');
         }
         document.write('</div>');
 
         // 详细的if语句逻辑展示
         document.write('<div class="result">');
         document.write('<h2>🧠 if语句逻辑分析</h2>');
 
         // 展示每一步的判断过程
         document.write('<h3>步骤1：找最大值</h3>');
         if (num1 >= num2 && num1 >= num3) {
             document.write('✅ if (num1 >= num2 && num1 >= num3) → ' + num1 + ' 是最大值<br>');
         } else if (num2 >= num1 && num2 >= num3) {
             document.write('✅ else if (num2 >= num1 && num2 >= num3) → ' + num2 + ' 是最大值<br>');
         } else {
             document.write('✅ else → ' + num3 + ' 是最大值<br>');
         }
 
         document.write('<h3>步骤2：找最小值</h3>');
         if (num1 <= num2 && num1 <= num3) {
             document.write('✅ if (num1 <= num2 && num1 <= num3) → ' + num1 + ' 是最小值<br>');
         } else if (num2 <= num1 && num2 <= num3) {
             document.write('✅ else if (num2 <= num1 && num2 <= num3) → ' + num2 + ' 是最小值<br>');
         } else {
             document.write('✅ else → ' + num3 + ' 是最小值<br>');
         }
 
         document.write('</div>');
     }
     */
  </script>

</body>

</html>