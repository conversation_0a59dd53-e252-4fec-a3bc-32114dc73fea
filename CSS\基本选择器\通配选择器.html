<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>通配选择器</title>
    <style>
        *{
            color: orange;
            font-size: 28px;
        }
    </style>
</head>
<!-- CSS的基本选择器
1.通配选择器
2.元素选择器
3.类选择器
4.id选择器

通配选择器
    作用：选择页面中所有元素
    语法：*
    例如：*{color:red;}
     -->
<body>
    <h1>欢迎来到土味官网，土的味道我知道</h1>
    <br>
    <h2>土味情话</h2>
    <h3>作者：优秀的网友们</h3>
    <p>万水千山总是情，爱我多点行不行！</p>
    <p>草莓、蓝莓、蔓越莓，今天你想我没？</p>
    <p>我心里给你留了一块地，我的死心塌地！</p>
</body>
</html>