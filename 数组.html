<script>
    function Person(name,age,gender){
        this.name=name;
        this.age=age;
        this.gender=gender;
    }
    var per=new Person("孫悟空",18,"男");
    var per2=new Person("豬八戒",28,"男");
    var per3=new Person("蜘蛛精",16,"女");
    var per4=new Person("红孩儿",8,"男");    
    var per5=new Person("二郎神",38,"男");

    var perArr=[per,per2,per3,per4,per5];

    function getArray(arr){
        var newArr=[];
        for (var i=0;i<arr.length;i++){
            var p = arr[i];
            if (p.age>=18){
                newArr.push(p);
            }
        }
        return newArr;

    }
    console.log(getArray(perArr));

</script>