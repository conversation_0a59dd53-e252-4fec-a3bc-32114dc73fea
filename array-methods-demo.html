<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 数组方法练习 Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .method-section {
            margin-bottom: 30px;
            padding: 15px;
            border-left: 4px solid #007acc;
            background-color: #f8f9fa;
        }
        .code-block {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .result {
            background-color: #e6fffa;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #38b2ac;
            margin: 10px 0;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a9e;
        }
        .interactive-section {
            background-color: #fff5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 JavaScript 数组方法练习 Demo</h1>
        
        <div class="interactive-section">
            <h2>🎯 互动练习区</h2>
            <p>点击按钮查看不同数组方法的效果：</p>
            <button onclick="runBasicMethods()">基础方法</button>
            <button onclick="runIterationMethods()">遍历方法</button>
            <button onclick="runSearchMethods()">查找方法</button>
            <button onclick="runTransformMethods()">转换方法</button>
            <button onclick="runAdvancedMethods()">高级方法</button>
            <button onclick="clearResults()">清空结果</button>
            <div id="output" class="result" style="min-height: 100px; margin-top: 15px;">
                点击上面的按钮开始练习！
            </div>
        </div>

        <div class="method-section">
            <h2>📚 数组方法分类说明</h2>
            
            <h3>1. 修改原数组的方法</h3>
            <div class="code-block">
// 添加/删除元素
let arr = [1, 2, 3];
arr.push(4);        // [1, 2, 3, 4] - 末尾添加
arr.pop();          // [1, 2, 3] - 删除最后一个
arr.unshift(0);     // [0, 1, 2, 3] - 开头添加
arr.shift();        // [1, 2, 3] - 删除第一个
arr.splice(1, 1, 'new'); // [1, 'new', 3] - 替换元素

// 排序
arr.sort();         // 排序
arr.reverse();      // 反转
            </div>

            <h3>2. 不修改原数组的方法</h3>
            <div class="code-block">
let numbers = [1, 2, 3, 4, 5];

// 创建新数组
let doubled = numbers.map(x => x * 2);     // [2, 4, 6, 8, 10]
let evens = numbers.filter(x => x % 2 === 0); // [2, 4]
let sliced = numbers.slice(1, 3);          // [2, 3]

// 查找
let found = numbers.find(x => x > 3);      // 4
let index = numbers.indexOf(3);            // 2
let hasTwo = numbers.includes(2);          // true

// 测试
let allPositive = numbers.every(x => x > 0); // true
let hasEven = numbers.some(x => x % 2 === 0); // true
            </div>
        </div>
    </div>

    <script>
        // 示例数据
        const students = [
            { name: '张三', age: 20, score: 85 },
            { name: '李四', age: 22, score: 92 },
            { name: '王五', age: 19, score: 78 },
            { name: '赵六', age: 21, score: 88 }
        ];

        const numbers = [1, 5, 3, 9, 2, 8, 4, 7, 6];
        const fruits = ['苹果', '香蕉', '橙子', '葡萄', '草莓'];

        function log(title, content) {
            const output = document.getElementById('output');
            output.innerHTML += `<h4>${title}</h4><pre>${content}</pre>`;
        }

        function clearResults() {
            document.getElementById('output').innerHTML = '点击上面的按钮开始练习！';
        }

        function runBasicMethods() {
            clearResults();
            log('🔧 基础操作方法', '');
            
            let arr = [...numbers]; // 复制数组
            log('原数组', JSON.stringify(arr));
            
            arr.push(10);
            log('push(10) 后', JSON.stringify(arr));
            
            let popped = arr.pop();
            log('pop() 返回值', popped + ', 数组变为: ' + JSON.stringify(arr));
            
            arr.unshift(0);
            log('unshift(0) 后', JSON.stringify(arr));
            
            arr.splice(2, 1, 'new');
            log('splice(2, 1, "new") 后', JSON.stringify(arr));
            
            let sorted = [...numbers].sort((a, b) => a - b);
            log('sort() 排序后', JSON.stringify(sorted));
        }

        function runIterationMethods() {
            clearResults();
            log('🔄 遍历方法', '');
            
            log('forEach - 打印每个水果', '');
            let forEachResult = '';
            fruits.forEach((fruit, index) => {
                forEachResult += `${index}: ${fruit}\n`;
            });
            log('', forEachResult);
            
            let doubled = numbers.map(x => x * 2);
            log('map - 每个数字乘以2', JSON.stringify(doubled));
            
            let sum = numbers.reduce((acc, curr) => acc + curr, 0);
            log('reduce - 求和', sum);
            
            let evens = numbers.filter(x => x % 2 === 0);
            log('filter - 筛选偶数', JSON.stringify(evens));
        }

        function runSearchMethods() {
            clearResults();
            log('🔍 查找方法', '');
            
            let found = students.find(s => s.score > 90);
            log('find - 找到分数>90的学生', JSON.stringify(found));
            
            let foundIndex = students.findIndex(s => s.age < 20);
            log('findIndex - 找到年龄<20的学生索引', foundIndex);
            
            let bananaIndex = fruits.indexOf('香蕉');
            log('indexOf - 香蕉的位置', bananaIndex);
            
            let hasApple = fruits.includes('苹果');
            log('includes - 是否包含苹果', hasApple);
            
            let allAdult = students.every(s => s.age >= 18);
            log('every - 是否都是成年人', allAdult);
            
            let someHighScore = students.some(s => s.score > 90);
            log('some - 是否有人分数>90', someHighScore);
        }

        function runTransformMethods() {
            clearResults();
            log('🔄 转换方法', '');
            
            let joined = fruits.join(' - ');
            log('join - 用" - "连接水果', joined);
            
            let sliced = numbers.slice(2, 5);
            log('slice(2, 5) - 截取片段', JSON.stringify(sliced));
            
            let concatenated = fruits.concat(['芒果', '菠萝']);
            log('concat - 连接新水果', JSON.stringify(concatenated));
            
            let nested = [[1, 2], [3, 4], [5, 6]];
            let flattened = nested.flat();
            log('flat - 扁平化嵌套数组', JSON.stringify(flattened));
            
            let mapped = fruits.slice(0, 3).map(fruit => [fruit, fruit.length]);
            log('map - 水果和长度的映射', JSON.stringify(mapped));
        }

        function runAdvancedMethods() {
            clearResults();
            log('🚀 高级方法', '');
            
            // 链式调用示例
            let result = students
                .filter(s => s.age >= 20)
                .map(s => ({ ...s, grade: s.score >= 90 ? 'A' : s.score >= 80 ? 'B' : 'C' }))
                .sort((a, b) => b.score - a.score);
            
            log('链式调用 - 筛选成年人→添加等级→按分数排序', JSON.stringify(result, null, 2));
            
            // 分组示例
            let grouped = students.reduce((acc, student) => {
                let ageGroup = student.age >= 21 ? '21+' : '20-';
                if (!acc[ageGroup]) acc[ageGroup] = [];
                acc[ageGroup].push(student);
                return acc;
            }, {});
            
            log('reduce - 按年龄分组', JSON.stringify(grouped, null, 2));
            
            // 去重示例
            let duplicates = [1, 2, 2, 3, 3, 3, 4, 5, 5];
            let unique = [...new Set(duplicates)];
            log('Set去重', `原数组: ${JSON.stringify(duplicates)}\n去重后: ${JSON.stringify(unique)}`);
            
            // Array.from 示例
            let range = Array.from({length: 5}, (_, i) => i + 1);
            log('Array.from - 创建1-5的数组', JSON.stringify(range));
        }

        // 页面加载完成后显示欢迎信息
        window.onload = function() {
            log('🎉 欢迎使用数组方法练习器！', '选择上面的按钮开始探索不同的数组方法吧！');
        };
    </script>
</body>
</html>
