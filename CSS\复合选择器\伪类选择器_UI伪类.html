<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>伪类选择器_UI伪类</title>

    <style>
        /* 选中的是被选中的复选框或单选框 */
        input:checked{
            width: 100px;
            height: 100px;
        }

        /* 选中的是被禁用的input元素 */
        input:disabled{
            background-color: gray;
        }

        /* 选中的是未被禁用的input元素 */
        input:enabled{
            background-color: green;
        }
    </style>
</head>
<body>
    <input type="checkbox">
    <input type="radio" name="gender">
    <input type="radio" name="gender">
    <input type="text">
    <input type="text" disabled>
</body>
</html>