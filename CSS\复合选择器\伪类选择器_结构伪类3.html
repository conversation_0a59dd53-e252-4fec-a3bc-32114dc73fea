<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结构伪类3</title>
    <style>
        /* 选中的是div的第n个儿子P元素（按照所有同类型兄弟计算） —— 看结构一 */
        /* div>p:nth-last-child(3){
            color: red;
        } */

        /* 选中的是div的第n个儿子P元素（按照所有同类型兄弟计算） —— 看结构二 */
        /* div>p:nth-last-of-type(2){
            color: red;
        } */

        /* 选中的是没有兄弟的span元素 —— 看结构三 */
        /* span:only-child {
            color: red;
        } */

        /* 选中的是没有同类型兄弟的span元素 —— 看结构三 */
        /* span:only-of-type {
            color: red;
        } */

        /* 选中的是根元素 */
        /* :root{
            background-color: gray;
        } */

        /* 选中的是没有子元素的div元素 —— 看结构四 */
        div:empty{
            width: 100px;
            height: 100px;
            background-color: red;
        }
    </style>
</head>

<body>
    <!-- 结构一 -->
    <!-- <div>
        <span>测试1</span>
        <p>张三：98分</p>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
        <p>孙七：58分</p>
        <p>周八：48分</p>
        <span>测试2</span>
    </div> -->

    <!-- 结构二 -->
    <!-- <div>
        <span>测试1</span>
        <p>张三：98分</p>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
        <p>孙七：58分</p>
        <p>周八：48分</p>
        <span>测试2</span>
    </div> -->

    <!-- 结构三 -->
    <!-- <div>
        <span>测试1</span>
    </div>
    <div>
        <span>测试2</span>
        <p>张三：98分</p>
        <p>李四：88分</p>
        <p>王五：78分</p>
        <p>赵六：68分</p>
        <p>孙七：58分</p>
        <p>周八：48分</p>
        <span>测试2</span>
    </div> -->

    <!-- 结构四 -->
     <div></div>

</body>

</html>