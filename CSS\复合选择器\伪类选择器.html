<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>伪类选择器</title>
    <!-- 什么是伪类选择器？
    伪类选择器：选择页面中满足某种状态的元素
    例如：
    a:hover{color:red;} 选中a标签鼠标悬停的状态
    a:visited{color:green;} 选中a标签访问过的状态
    a:active{color:blue;} 选中a标签点击的状态
    a:link{color:yellow;} 选中a标签未访问的状态 -->

   
    <style>
         /* 选中的是没有访问过的a标签 */
        a:link {
            color: orange;
        }

        /* 选中的是访问过的a标签 */
        a:visited {
            color: green;
        }
    </style>

</head>

<body>
    <a href="www.baidu.com">去百度</a>
    <a href="www.jd.com">去京东</a>
</body>

</html>