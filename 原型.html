<script>
    /*function Person(name, age,gender) {
        this.name=name;
        this.age=age;
        this.gender=gender;

    }
    Person.prototype.sayName=function(){
        alert("Hello,大家好，我是"+this.name);
    }

    var per1=new Person("孫悟空",18,"男");
    var per2=new Person("豬八戒",28,"男");
    per1.sayName();
    per2.sayName();*/

    function MyClass(){

    }
    MyClass.prototype.name="我是原型中的name";

    var mc=new MyClass();
    mc.age=18;
   // console.log(mc.name);
   // console.log("name" in mc);
   console.log(mc.hasOwnProperty("age"));



</script> 